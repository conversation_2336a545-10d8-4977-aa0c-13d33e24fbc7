"use client";

import React, { useState, useEffect, useRef } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { <PERSON>u, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Star } from "lucide-react";

const Header = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const menuRef = useRef(null);
  const hamburgerRef = useRef(null);
  const pathname = usePathname();

  // Navigation routes - Luxury brand style
  const navigationRoutes = [
    { id: 1, label: "INVENTORY", path: "https://inventory.diamondatelier.in/", external: true },
    { id: 2, label: "SHAPES", path: "/shapes" },
    { id: 4, label: "EDUCATION", path: "/education" },
    { id: 5, label: "ABOUT", path: "/about" },
    { id: 6, label: "CONTACT", path: "/contact" }
  ];

  // Load animation
  useEffect(() => {
    const timer = setTimeout(() => setIsLoaded(true), 100);
    return () => clearTimeout(timer);
  }, []);

  // Enhanced scroll detection with smooth transitions
  useEffect(() => {
    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          const scrollY = window.scrollY;
          setIsScrolled(scrollY > 10);
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        menuOpen &&
        menuRef.current &&
        !menuRef.current.contains(event.target) &&
        hamburgerRef.current &&
        !hamburgerRef.current.contains(event.target)
      ) {
        setMenuOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [menuOpen]);

  const toggleMenu = () => setMenuOpen(!menuOpen);
  const closeMenu = () => setMenuOpen(false);
  const isActive = (item) => pathname === item.path;

  return (
    <header className={`fixed top-0 w-full z-[100] transition-all duration-700 ease-out ${
      isScrolled
        ? 'bg-white/96 backdrop-blur-2xl shadow-2xl border-b border-gray-200/50'
        : 'bg-gradient-to-b from-white/98 via-white/95 to-white/90 backdrop-blur-md'
    } ${isLoaded ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'}`}>

        {/* Luxury Top Border with Shimmer Effect */}
        <div className="relative w-full h-0.5 bg-gradient-to-r from-transparent via-gray-300 to-transparent overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent animate-pulse"></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 xl:px-12 2xl:px-16">

          {/* Enhanced Top Row with Better Mobile Layout */}
          <div className="flex items-center justify-between lg:justify-center py-2 sm:py-2 lg:py-3">
            {/* Stylized Mobile Menu Button */}
            <button
              ref={hamburgerRef}
              onClick={toggleMenu}
              className={`lg:hidden relative p-3 rounded-xl transition-all duration-300 z-10 group ${
                menuOpen
                  ? 'bg-gray-900 text-white shadow-lg scale-105'
                  : 'bg-gray-50 text-gray-700 hover:bg-gray-100 hover:text-black hover:shadow-md'
              }`}
              aria-label="Toggle menu"
            >
              <div className="relative w-5 h-5">
                {menuOpen ? (
                  <X className="w-5 h-5 transition-transform duration-300 rotate-90" />
                ) : (
                  <Menu className="w-5 h-5 transition-transform duration-300 group-hover:scale-110" />
                )}
              </div>
            </button>

            {/* Enhanced Logo with Diamond Accent */}
            <Link href="/" className="group relative flex-1 lg:flex-none">
              <div className="text-center relative">
                {/* Diamond Icon Accent */}
                <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-all duration-500">
                  <Diamond className="w-4 h-4 text-gray-400 animate-pulse" />
                </div>

                <h1
                  className={`font-light text-black tracking-[0.25em] transition-all duration-700 group-hover:tracking-[0.35em] relative ${
                    isScrolled ? 'text-base sm:text-lg lg:text-xl' : 'text-lg sm:text-xl lg:text-2xl'
                  }`}
                  style={{
                    fontFamily: 'Playfair Display, Times New Roman, serif',
                    textShadow: '0 2px 4px rgba(0,0,0,0.08)'
                  }}
                >
                  DIAMOND ATELIER
                  {/* Sparkle Effects */}
                  <Sparkles className="absolute -top-1 -right-2 w-3 h-3 text-gray-400 opacity-0 group-hover:opacity-100 transition-all duration-500 animate-pulse" />
                  <Star className="absolute -bottom-1 -left-2 w-2 h-2 text-gray-400 opacity-0 group-hover:opacity-100 transition-all duration-700 animate-pulse" />
                </h1>

                {/* Enhanced Underline Effect */}
                <div className="relative mt-1 h-0.5 overflow-hidden">
                  <div className="w-full h-full bg-gradient-to-r from-transparent via-gray-200 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-gray-400 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-700 transform -translate-x-full group-hover:translate-x-full"></div>
                </div>
              </div>

              {/* Enhanced Glow Effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-black/2 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-1000 blur-3xl scale-110"></div>
            </Link>

            {/* Mobile Balance Spacer */}
            <div className="lg:hidden w-12"></div>
          </div>

          {/* Elegant Separator with Animation */}
          <div className="relative w-full h-px bg-gradient-to-r from-transparent via-gray-200 to-transparent overflow-hidden">
            <div className={`absolute inset-0 bg-gradient-to-r from-transparent via-gray-400 to-transparent transition-all duration-1000 ${
              isScrolled ? 'opacity-60' : 'opacity-30'
            }`}></div>
          </div>

          {/* Enhanced Navigation Row */}
          <div className="flex items-center justify-center py-2 lg:py-3">
            <nav className="hidden lg:flex items-center justify-between w-full max-w-4xl">
              {navigationRoutes.map((item, index) => {
                const linkClasses = `relative text-sm font-medium text-gray-600 hover:text-black transition-all duration-500 tracking-[0.15em] group uppercase px-3 py-1 rounded-lg hover:bg-gray-50/50 ${
                  isActive(item) ? "text-black bg-gray-50" : ""
                }`;

                const linkContent = (
                  <>
                    <span className="relative z-10">{item.label}</span>

                    {/* Enhanced Underline Effect */}
                    <span className={`absolute -bottom-1 left-1/2 transform -translate-x-1/2 h-0.5 bg-gradient-to-r from-gray-400 via-black to-gray-400 transition-all duration-500 ${
                      isActive(item) ? 'w-3/4 opacity-100' : 'w-0 opacity-0 group-hover:w-3/4 group-hover:opacity-100'
                    }`}></span>

                    {/* Hover Background Effect */}
                    <span className="absolute inset-0 bg-gradient-to-r from-transparent via-gray-100/30 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 rounded-lg scale-95 group-hover:scale-100"></span>

                    {/* Active State Indicator */}
                    {isActive(item) && (
                      <span className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-black rounded-full animate-pulse"></span>
                    )}
                  </>
                );

                return item.external ? (
                  <a
                    key={item.id}
                    href={item.path}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={linkClasses}
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    {linkContent}
                  </a>
                ) : (
                  <Link
                    key={item.id}
                    href={item.path}
                    className={linkClasses}
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    {linkContent}
                  </Link>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Enhanced Mobile Menu Background Overlay */}
        {menuOpen && (
          <div
            className="lg:hidden fixed inset-0 bg-gradient-to-b from-black/60 via-black/50 to-black/40 z-[98] backdrop-blur-sm"
            onClick={closeMenu}
          />
        )}

        {/* Redesigned Mobile Menu */}
        {menuOpen && (
          <div className="lg:hidden fixed top-0 left-0 right-0 bg-white z-[99] shadow-2xl border-b border-gray-200 animate-fadeIn mobile-menu-slide">
            {/* Mobile Menu Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-100">
              <div className="flex items-center space-x-2">
                <Diamond className="w-5 h-5 text-gray-600" />
                <span className="text-lg font-medium text-gray-800 tracking-wide">MENU</span>
              </div>
              <button
                onClick={closeMenu}
                className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors duration-200"
              >
                <X className="w-5 h-5 text-gray-600" />
              </button>
            </div>

            {/* Mobile Navigation */}
            <nav ref={menuRef} className="flex flex-col py-2">
              {navigationRoutes.map((item, index) => {
                const linkClasses = `relative flex items-center justify-between px-4 py-3 text-base font-medium text-gray-700 hover:text-black hover:bg-gray-50 transition-all duration-300 tracking-[0.1em] uppercase group ${
                  isActive(item) ? "text-black bg-gray-50 font-semibold" : ""
                }`;

                const linkContent = (
                  <>
                    <span className="flex items-center space-x-3">
                      <span className={`w-2 h-2 rounded-full transition-all duration-300 ${
                        isActive(item) ? 'bg-black' : 'bg-gray-300 group-hover:bg-gray-500'
                      }`}></span>
                      <span>{item.label}</span>
                    </span>

                    {/* Arrow Indicator */}
                    <span className={`transform transition-transform duration-300 ${
                      isActive(item) ? 'translate-x-0 opacity-100' : 'translate-x-2 opacity-0 group-hover:translate-x-0 group-hover:opacity-100'
                    }`}>
                      →
                    </span>
                  </>
                );

                return item.external ? (
                  <a
                    key={item.id}
                    href={item.path}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={linkClasses}
                    onClick={closeMenu}
                    style={{ animationDelay: `${index * 50}ms` }}
                  >
                    {linkContent}
                  </a>
                ) : (
                  <Link
                    key={item.id}
                    href={item.path}
                    className={linkClasses}
                    onClick={closeMenu}
                    style={{ animationDelay: `${index * 50}ms` }}
                  >
                    {linkContent}
                  </Link>
                );
              })}
            </nav>

            {/* Mobile Menu Footer */}
            <div className="px-4 py-3 border-t border-gray-100 bg-gray-50">
              <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
                <Sparkles className="w-4 h-4" />
                <span>Luxury Diamond Collection</span>
                <Sparkles className="w-4 h-4" />
              </div>
            </div>
          </div>
        )}
      </header>
  );
};

export default Header;
