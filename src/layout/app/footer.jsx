"use client";
import { FaInstagram, FaFacebook, FaLinkedinIn, FaPhone, FaEnvelope, FaMapMarkerAlt } from "react-icons/fa";
import Link from "next/link";

function Footer() {
    const quickLinks = [
        { label: "Shapes", href: "/shapes" },
        { label: "Education", href: "/education" },
        { label: "About", href: "/about" },
        { label: "Contact", href: "/contact" },
    ];

    const socialLinks = [
        { icon: <FaFacebook />, href: "#", label: "Facebook" },
        { icon: <FaInstagram />, href: "#", label: "Instagram" },
        { icon: <FaLinkedinIn />, href: "#", label: "LinkedIn" },
    ];

    return (
        <footer className="bg-gradient-to-br from-gray-900 via-black to-gray-800 text-white">
            {/* Main Footer Content */}
            <div className="max-w-7xl mx-auto px-6 py-16">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">

                    {/* Brand Section */}
                    <div className="lg:col-span-1">
                        <h2 className="text-2xl font-light tracking-[0.2em] mb-6"
                            style={{ fontFamily: 'Playfair Display, serif' }}>
                            DIAMOND ATELIER
                        </h2>
                        <p className="text-gray-300 leading-relaxed mb-6">
                            Crafting exceptional lab-grown diamonds with precision,
                            sustainability, and uncompromising quality.
                        </p>
                        <div className="flex space-x-4">
                            {socialLinks.map((social, index) => (
                                <a
                                    key={index}
                                    href={social.href}
                                    className="w-10 h-10 bg-gray-800 hover:bg-gray-700 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110"
                                    aria-label={social.label}
                                >
                                    {social.icon}
                                </a>
                            ))}
                        </div>
                    </div>

                    {/* Quick Links */}
                    <div>
                        <h3 className="text-lg font-semibold mb-6 tracking-wide">QUICK LINKS</h3>
                        <ul className="space-y-3">
                            {quickLinks.map((link, index) => (
                                <li key={index}>
                                    <Link
                                        href={link.href}
                                        className="text-gray-300 hover:text-white transition-colors duration-300 hover:underline"
                                    >
                                        {link.label}
                                    </Link>
                                </li>
                            ))}
                        </ul>
                    </div>

                    {/* Contact Info */}
                    <div>
                        <h3 className="text-lg font-semibold mb-6 tracking-wide">CONTACT INFO</h3>
                        <div className="space-y-4">
                            <div className="flex items-start space-x-3">
                                <FaPhone className="text-gray-400 mt-1 flex-shrink-0" />
                                <div>
                                    <p className="text-gray-300">+1 (123) 4567 8901</p>
                                    <p className="text-sm text-gray-400">Toll Free</p>
                                </div>
                            </div>
                            <div className="flex items-start space-x-3">
                                <FaEnvelope className="text-gray-400 mt-1 flex-shrink-0" />
                                <div>
                                    <p className="text-gray-300"><EMAIL></p>
                                    <p className="text-sm text-gray-400">Email Us</p>
                                </div>
                            </div>
                            <div className="flex items-start space-x-3">
                                <FaMapMarkerAlt className="text-gray-400 mt-1 flex-shrink-0" />
                                <div>
                                    <p className="text-gray-300">38 West 48th Street</p>
                                    <p className="text-gray-300">5th Floor, New York, NY 10036</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Newsletter */}
                    <div>
                        <h3 className="text-lg font-semibold mb-6 tracking-wide">STAY UPDATED</h3>
                        <p className="text-gray-300 mb-4">
                            Subscribe to our newsletter for the latest updates and exclusive offers.
                        </p>
                        <div className="space-y-3">
                            <input
                                type="email"
                                placeholder="Enter your email"
                                className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:border-gray-500 text-white placeholder-gray-400"
                            />
                            <button className="w-full bg-white text-black py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors duration-300">
                                SUBSCRIBE
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Bottom Bar */}
            <div className="border-t border-gray-800">
                <div className="max-w-7xl mx-auto px-6 py-6">
                    <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                        <p className="text-gray-400 text-sm">
                            &copy; 2025 Diamond Atelier. All rights reserved.
                        </p>
                        <div className="flex space-x-6 text-sm">
                            <a href="#" className="text-gray-400 hover:text-white transition-colors">Privacy Policy</a>
                            <a href="#" className="text-gray-400 hover:text-white transition-colors">Terms of Service</a>
                            <a href="#" className="text-gray-400 hover:text-white transition-colors">Cookie Policy</a>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
    );
}

export default Footer;
