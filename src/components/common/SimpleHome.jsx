"use client";
import { useState,useEffect } from "react";
import Image from "next/image";
import AnimatedHeading from "./AnimatedHeading";
import { ChevronDown } from "lucide-react";
import Link from "next/link"
import { motion, useInView } from "framer-motion";

const sections = [
  // {
  //   id: "hero",
  //   title: "Exquisite Lab Grown Diamonds",
  //   subtitle: "Every Diamond Tells a Story",
  //   description:
  //     "Ethically sourced, certified diamonds with unparalleled quality and brilliance.",
  //   image: "/image/tache_diamond_rough_cut.png",
  //   cta: "Discover Our Collection",
  // },
  {
    id: "shapes",
    title: "100+ shapes",
    subtitle: "Diamonds over 100+ shapes",
    description:
      "Our artisan workshop offers 100+ diamond shapes, crafted with elegance and precision. We also create custom shapes tailored to your vision within 30 days.",
    image: "/image/diamond-cuts.jpg",
    // video: "/images/about/Shapes video.mp4",
    cta: "Discover Shapes",
  },
  {
    id: "matching-layout",
    title: "Matching layout",
    subtitle: "Calibrated consistency from 10–99 cents",
    description:
      "Our comprehensive collection of calibrated matching layouts ranges from 10 to 99 cents, with a 0.10 MM tolerance. Whether you seek uniformity in shapes, colors, clarities, dimensions, or cent values, trust us to deliver precisely what you need.",
    image: "/image/shaping_phase_tache-768x768.png",
    video: "/images/about/Matching Layout video.mp4",
    cta: "View Our Process",
  },
  {
    id: "colors",
    title: "30+ colors",
    subtitle: "Nature's Spectrum, Perfected Diamonds colors over 30+ colors",
    description:
      "We specialize in unique colored diamonds across 10+ fancy shades, with customized color delivery guaranteed within 20 days.",
    image: "/images/Colors/natural-color-dmds-color-diamonds-rough-diamonds-removebg-preview.png",
    video: null, // Use image instead of video
    cta: "Explore Colors",
  },
  {
    id: "star-melee",
    title: "Star Melee",
    subtitle: "Small-sized lab-grown diamonds",
    description:
      "We have the finest small-sized lab-grown diamonds: -2 (0.006 to 0.008 carats), Star ranging from (0.009 to 0.021 carats), and Melee (0.021 to 0.074 carats). Perfect for intricate jewelry designs.",
    image: "/images/about/star-melee.png",
    video: null,
    cta: "View Star Melee",
  },
];

export default function SimpleHome() {
  const [activeSection, setActiveSection] = useState(0);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return (
    <div className="bg-white min-h-screen pt-20">
      <div className="full-section relative w-full h-[50vh] sm:h-[60vh] md:h-[70vh] lg:h-[calc(100vh-80px)] overflow-hidden">
        {/* Background Image */}
        <Image
          src="/image/Homepage_Banner_2025_Desktop_c0ddfa8194.png"
          alt="Diamond Atelier Homepage Banner"
          fill
          className="absolute inset-0 w-full h-full object-cover md:object-cover sm:object-contain object-center"
          priority
          quality={90}
        />

        {/* Sophisticated Overlay Gradients */}
        <div className="absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-black/60 pointer-events-none" />
        <div className="absolute inset-0 bg-gradient-to-r from-black/20 via-transparent to-black/20 pointer-events-none" />

        {/* Animated Particles/Sparkles */}
        {isClient && (
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            {[...Array(12)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 bg-white rounded-full opacity-60"
                initial={{
                  x: Math.random() * window.innerWidth,
                  y: Math.random() * window.innerHeight,
                  scale: 0,
                }}
                animate={{
                  y: [null, -100],
                  opacity: [0.6, 0],
                  scale: [0, 1, 0],
                }}
                transition={{
                  duration: 3 + Math.random() * 2,
                  repeat: Infinity,
                  delay: Math.random() * 3,
                  ease: "easeOut",
                }}
              />
            ))}
          </div>
        )}

        {/* Main Content Container */}
        <div className="absolute inset-0 flex flex-col items-center justify-center z-20 px-4 sm:px-6 md:px-8">
          {/* Logo Section */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1.5, ease: "easeOut" }}
            className="text-center mb-8"
          >
            <div className="relative group">
              <h1
                className="text-lg sm:text-xl md:text-2xl lg:text-4xl xl:text-5xl font-light leading-tight tracking-[0.1em] sm:tracking-[0.2em] text-black/95 transition-all duration-700 font-montserrat"
                style={{
                  // textShadow: '0 0 40px rgba(255,255,255,0.6), 0 0 80px rgba(255,255,255,0.3)',
                  letterSpacing: "0.1em",
                }}
              >
                DIAMONDS THAT DESERVE YOU
              </h1>

              {/* Premium Glow Effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-black/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700 blur-3xl"></div>
            </div>
          </motion.div>

          {/* Elegant Tagline */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1.5, delay: 0.5, ease: "easeOut" }}
            className="text-center max-w-4xl mx-auto mb-12"
          >
            <h1 className="text-base sm:text-lg md:text-xl lg:text-2xl xl:text-3xl font-light text-black/90 tracking-[0.2em] sm:tracking-[0.3em] leading-relaxed font-montserrat">
              {/* <PermanentCounter
                targetNumber={10000}
                suffix="+ CERTIFIED STONES"
                duration={2000}
              /> */}{" "}
              10,000+ Certified Stones
            </h1>
            <div className="w-24 h-px bg-gradient-to-r from-transparent via-black/60 to-transparent mx-auto mt-6 mb-6"></div>
            <p className="text-sm sm:text-base md:text-lg lg:text-xl text-black/70 font-light tracking-wide leading-relaxed font-montserrat">
              Where precision meets perfection in every facet
            </p>
          </motion.div>

          {/* Call to Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1.5, delay: 1, ease: "easeOut" }}
            className="flex flex-col sm:flex-row gap-4 sm:gap-6"
          >
            <Link
              href="/shapes"
              className="group relative px-8 py-3 bg-Black border border-white/30 text-Black hover:bg-white hover:text-black transition-all duration-500 text-sm sm:text-base tracking-wider font-light overflow-hidden"
            >
              <span className="relative z-10">EXPlORE SHAPES</span>
              <div className="absolute inset-0 bg-Black transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></div>
            </Link>

            <Link
              href="https://inventory.diamondatelier.in/"
              className="group relative px-8 py-3 bg-black/10 backdrop-blur-sm border border-white/20 text-white hover:bg-white/20 transition-all duration-500 text-sm sm:text-base tracking-wider font-light"
            >
              <span className="relative z-10">VIEW INVENTORY</span>
            </Link>
          </motion.div>
        </div>

        {/* Scroll Indicator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1.5, delay: 1.5 }}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10 cursor-pointer"
          onClick={() => setCurrentIndex(1)}
        >
          <motion.div
            animate={{ y: [0, 8, 0] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            className="flex flex-col items-center text-white/60 hover:text-white transition-colors duration-300"
          >
            <span className="text-xs tracking-widest mb-2 font-light">
              SCROLL
            </span>
            <ChevronDown className="w-5 h-5" />
          </motion.div>
        </motion.div>
      </div>
      {/* Hero Section - Redesigned without circular image */}
      <section className="min-h-screen flex items-center justify-center px-4 sm:px-2 md:px-3 lg:px-4 xl:px-6 bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-full mx-auto w-full">
          {/* Desktop Layout */}
          <div className="hidden lg:flex items-center justify-between gap-16">
            {/* Left - Text Content */}
            <div className="flex-1 text-black">
              <div className="mb-6">
                <span className="text-gray-600 text-sm uppercase tracking-[0.2em] font-light font-montserrat">
                  {sections[0].subtitle}
                </span>
              </div>
              <AnimatedHeading
                text={sections[0].title}
                level="h1"
                className="text-5xl xl:text-6xl 2xl:text-7xl mb-8 font-light leading-tight font-montserrat"
                showUnderline={false}
                animationDelay={0.2}
                triggerOnMount={true}
              />
              <p className="text-xl xl:text-2xl leading-relaxed font-light text-gray-700 mb-12 max-w-2xl font-montserrat">
                {sections[0].description}
              </p>
              <Link href="/shapes" className="inline-block bg-black text-white px-8 py-4 text-lg font-medium uppercase tracking-[0.1em] hover:bg-gray-800 transition-colors duration-300 font-montserrat">
                {sections[0].cta}
              </Link>
            </div>

            {/* Right - Modern Card Design */}
            <div className="flex-1 flex justify-center">
              <div className="relative w-full max-w-lg">
                <div className="bg-white rounded-2xl shadow-2xl p-8 border border-gray-100">
                  <div className="relative h-64 mb-6 overflow-hidden rounded-xl">
                    <Image
                      src={sections[0].image}
                      alt={sections[0].title}
                      fill
                      className="object-cover"
                      priority
                    />
                  </div>
                  <div className="text-center">
                    <h3 className="text-2xl text-black font-light mb-4 font-montserrat">Premium Diamond Cuts</h3>
                    <p className="text-gray-600 font-light font-montserrat">Masterfully crafted for maximum brilliance</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Mobile Layout */}
          <div className="lg:hidden">
            {/* Mobile Section Container with proper spacing */}
            <div className="bg-white mx-4 my-8 rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
              {/* Section Header with elegant styling */}
              <div className="bg-gradient-to-r from-gray-50 to-white px-6 py-4 border-b border-gray-100">
                <div className="text-center">
                  <span className="text-gray-500 text-xs uppercase tracking-[0.3em] font-light font-montserrat block mb-2">
                    {sections[0].subtitle}
                  </span>
                  <AnimatedHeading
                    text={sections[0].title}
                    level="h2"
                    className="text-2xl sm:text-3xl font-light leading-tight font-montserrat text-black"
                    showUnderline={false}
                    animationDelay={0.2}
                    triggerOnMount={true}
                  />
                </div>
              </div>

              {/* Section Content */}
              <div className="p-6 text-center text-black">
                {/* Mobile Image Content */}
                <div className="mb-6">
                  <div className="relative h-48 overflow-hidden rounded-xl mx-auto max-w-xs">
                    <Image
                      src={sections[0].image}
                      alt={sections[0].title}
                      fill
                      className="object-cover"
                      priority
                    />
                  </div>
                </div>

                {/* Section Description */}
                <p className="text-sm leading-relaxed font-light text-gray-600 mb-6 font-montserrat">
                  {sections[0].description}
                </p>

                {/* Action Buttons */}
                <div className="flex flex-col gap-3">
                  <Link href="/shapes" className="bg-black text-white px-6 py-3 text-sm font-medium uppercase tracking-[0.1em] hover:bg-gray-800 transition-all duration-300 font-montserrat rounded-md">
                    {sections[0].cta}
                  </Link>
                  <button className="text-gray-500 px-6 py-2 text-xs font-light uppercase tracking-[0.1em] hover:text-black transition-colors duration-300 font-montserrat">
                    Learn More
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Other Sections - Redesigned without circular images */}
      {sections.slice(1).map((section, index) => (
        <section
          key={section.id}
          className={`min-h-screen flex items-center justify-center px-4 sm:px-2 md:px-3 lg:px-4 xl:px-6 ${
            index % 2 === 0 ? 'bg-white' : 'bg-gray-50'
          }`}
        >
          <div className="max-w-full mx-auto w-full">
            {/* Desktop Layout - Modern Grid */}
            <div className="hidden md:grid md:grid-cols-1 lg:grid-cols-2 gap-8 md:gap-12 lg:gap-16 xl:gap-20 items-center w-full">
              {/* Text Content */}
              <div className={`text-black ${index % 2 === 0 ? 'lg:order-2' : 'lg:order-1'} md:text-center lg:text-left`}>
                <div className="mb-4 md:mb-6">
                  <span className="text-gray-600 text-xs sm:text-sm uppercase tracking-[0.2em] font-light font-montserrat">
                    {section.subtitle}
                  </span>
                </div>
                <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-4xl xl:text-5xl mb-4 md:mb-6 font-light leading-tight font-montserrat">
                  {section.title}
                </h2>
                <p className="text-base sm:text-lg md:text-lg lg:text-lg xl:text-xl leading-relaxed font-light text-gray-700 mb-6 md:mb-8 font-montserrat max-w-2xl md:mx-auto lg:mx-0">
                  {section.description}
                </p>
                <div className="flex flex-col sm:flex-row gap-3 md:gap-4 justify-center lg:justify-start">
                  <button className="border border-black text-black px-6 md:px-8 py-2 md:py-3 text-sm md:text-base font-medium uppercase tracking-[0.1em] hover:bg-black hover:text-white transition-all duration-300 font-montserrat">
                    {section.cta}
                  </button>
                  <button className="text-gray-600 px-6 md:px-8 py-2 md:py-3 text-sm md:text-base font-light uppercase tracking-[0.1em] hover:text-black transition-colors duration-300 font-montserrat">
                    Learn More
                  </button>
                </div>
              </div>

              {/* Modern Video/Image Layout */}
              <div className={`${index % 2 === 0 ? 'lg:order-1' : 'lg:order-2'} flex justify-center mb-8 lg:mb-0`}>
                {section.id === 'star-melee' ? (
                  /* Star Melee Card Box */
                  <div className="relative w-full max-w-lg">
                    <div className="bg-white rounded-2xl shadow-2xl p-8 border border-gray-100">
                      <div className="relative h-64 mb-6 overflow-hidden rounded-xl">
                        <Image
                          src={section.image}
                          alt={section.title}
                          fill
                          className="object-contain"
                        />
                      </div>
                      <div className="text-center">
                        <h3 className="text-2xl text-black font-light mb-4 font-montserrat">Premium Star Melee</h3>
                        <p className="text-gray-600 font-light font-montserrat">Perfect for intricate jewelry designs</p>
                      </div>
                    </div>
                  </div>
                ) : section.id === 'colors' ? (
                  /* 30+ Colors Card Box */
                  <div className="relative w-full max-w-lg">
                    <div className="bg-white rounded-2xl shadow-2xl p-8 border border-gray-100">
                      <div className="relative h-64 mb-6 overflow-hidden rounded-xl">
                        <Image
                          src={section.image}
                          alt={section.title}
                          fill
                          className="object-contain"
                        />
                      </div>
                      <div className="text-center">
                        <h3 className="text-2xl text-black font-light mb-4 font-montserrat">Premium Fancy Colors</h3>
                        <p className="text-gray-600 font-light font-montserrat">30+ stunning diamond colors available</p>
                      </div>
                    </div>
                  </div>
                ) : (
                  /* Regular Video/Image Container */
                  <div className="relative w-full max-w-sm md:max-w-md lg:max-w-lg">
                    {/* Main Media Container */}
                    <div className={`relative overflow-hidden rounded-2xl shadow-2xl mx-auto ${
                      section.id === 'shapes' ? 'h-48 sm:h-56 md:h-64 lg:h-72 w-full max-w-xs sm:max-w-sm md:max-w-md lg:w-80' : 'h-56 sm:h-64 md:h-80 lg:h-96 w-full'
                    }`}>
                      {section.video ? (
                        <video
                          autoPlay
                          loop
                          muted
                          playsInline
                          className="w-full h-full object-cover"
                          ref={(video) => {
                            if (video) {
                              video.playbackRate = 2.5;
                            }
                          }}
                        >
                          <source src={section.video} type="video/mp4" />
                          <Image
                            src={section.image}
                            alt={section.title}
                            fill
                            className="object-cover"
                          />
                        </video>
                      ) : (
                        <Image
                          src={section.image}
                          alt={section.title}
                          fill
                          className="object-cover"
                        />
                      )}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                    </div>
                  </div>
                )}

                {/* Floating Info Card - Only for non-card sections */}
                {section.id !== 'star-melee' && (
                  /* <div className="absolute -bottom-4 -right-4 bg-white rounded-xl shadow-xl p-4 max-w-xs">
                    <h4 className="text-base font-medium mb-2 font-montserrat">
                      {section.id === 'shapes' && '100+ Diamond Shapes'}
                      {section.id === 'matching-layout' && 'Calibrated Precision'}
                      {section.id === 'colors' && '30+ Fancy Colors'}
                    </h4>
                    <p className="text-xs text-gray-600 font-light font-montserrat">
                      {section.id === 'shapes' && 'Custom shapes in 30 days'}
                      {section.id === 'matching-layout' && '0.10 MM tolerance guarantee'}
                      {section.id === 'colors' && 'Custom colors in 20 days'}
                    </p>
                  </div> */
                  null
                )}
              </div>
            </div>

            {/* Mobile/Tablet Layout */}
            <div className="md:hidden">
              {/* Mobile Section Container with proper spacing */}
              <div className="bg-white mx-3 sm:mx-4 my-6 sm:my-8 rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                {/* Section Header with elegant styling */}
                <div className="bg-gradient-to-r from-gray-50 to-white px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-100">
                  <div className="text-center">
                    <span className="text-gray-500 text-xs uppercase tracking-[0.2em] sm:tracking-[0.3em] font-light font-montserrat block mb-1 sm:mb-2">
                      {section.subtitle}
                    </span>
                    <h2 className="text-xl sm:text-2xl font-light leading-tight font-montserrat text-black">
                      {section.title}
                    </h2>
                  </div>
                </div>

                {/* Section Content */}
                <div className="p-4 sm:p-6 text-center text-black">

                  {/* Mobile Image/Video Content */}
                  <div className="mb-4 sm:mb-6">
                    {section.id === 'star-melee' ? (
                      /* Star Melee Image */
                      <div className="relative h-40 sm:h-48 overflow-hidden rounded-xl mx-auto max-w-xs">
                        <Image
                          src={section.image}
                          alt={section.title}
                          fill
                          className="object-contain"
                        />
                      </div>
                    ) : section.id === 'colors' ? (
                      /* 30+ Colors Image */
                      <div className="relative h-48 overflow-hidden rounded-xl mx-auto max-w-xs">
                        <Image
                          src={section.image}
                          alt={section.title}
                          fill
                          className="object-contain"
                        />
                      </div>
                    ) : (
                      /* Regular Video/Image */
                      <div className={`relative overflow-hidden rounded-xl mx-auto ${
                        section.id === 'shapes' ? 'h-40 w-56' : 'h-48 max-w-xs w-full'
                      }`}>
                        {section.video ? (
                          <video
                            autoPlay
                            loop
                            muted
                            playsInline
                            className="w-full h-full object-cover"
                            ref={(video) => {
                              if (video) {
                                video.playbackRate = 2.5;
                              }
                            }}
                          >
                            <source src={section.video} type="video/mp4" />
                            <Image
                              src={section.image}
                              alt={section.title}
                              fill
                              className="object-cover"
                            />
                          </video>
                        ) : (
                          <Image
                            src={section.image}
                            alt={section.title}
                            fill
                            className="object-cover"
                          />
                        )}
                      </div>
                    )}
                  </div>

                  {/* Section Description */}
                  <p className="text-sm leading-relaxed font-light text-gray-600 mb-6 font-montserrat">
                    {section.description}
                  </p>

                  {/* Action Buttons */}
                  <div className="flex flex-col gap-3">
                    <button className="bg-black text-white px-6 py-3 text-sm font-medium uppercase tracking-[0.1em] hover:bg-gray-800 transition-all duration-300 font-montserrat rounded-md">
                      {section.cta}
                    </button>
                    <button className="text-gray-500 px-6 py-2 text-xs font-light uppercase tracking-[0.1em] hover:text-black transition-colors duration-300 font-montserrat">
                      Learn More
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      ))}

      <section className="relative w-full h-96 overflow-hidden">
        <div
          className="absolute inset-0 w-full h-full bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: "url('/image/Expert.jpg')",
          }}
        ></div>
        <div className="absolute inset-0 bg-gradient-to-r from-black/40 to-transparent"></div>
        <div className="relative z-10 h-full flex items-center">
          <div className="max-w-full mx-auto px-4 sm:px-2 md:px-3 lg:px-4 xl:px-6 w-full">
            <div className="max-w-2xl">
              <h2 className="text-3xl sm:text-4xl lg:text-5xl font-light text-black mb-6 drop-shadow-lg font-montserrat">
                Expert Eyes on Every Stone
              </h2>
              <p className="text-lg text-black mb-8 leading-relaxed drop-shadow-md font-light font-montserrat">
                Our in-house gemologists personally inspect and verify every
                lab-grown diamond. You&apos;ll receive a detailed report
                covering brilliance, cut, and quality — far beyond what a
                certificate alone can show.
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
