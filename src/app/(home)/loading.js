export default function Loading() {
  return (
    <div className="bg-black min-h-screen flex items-center justify-center">
      <div className="text-center">
        {/* Video placeholder */}
        <div className="w-full h-[28vh] md:h-[90vh] xl:h-screen bg-gray-800 animate-pulse rounded-lg mb-8"></div>
        
        <div className="space-y-8 px-6">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-700 rounded w-3/4 mx-auto mb-4"></div>
            <div className="h-4 bg-gray-700 rounded w-1/2 mx-auto"></div>
          </div>
        
        

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {[1, 2, 3, 4].map((item) => (
              <div key={item} className="animate-pulse">
                <div className="h-64 bg-gray-700 rounded-lg mb-4"></div>
                <div className="h-4 bg-gray-700 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-700 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
