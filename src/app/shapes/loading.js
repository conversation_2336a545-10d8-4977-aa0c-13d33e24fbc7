export default function Loading() {
  return (
    <div className="bg-black min-h-screen py-8">
      <div className="container mx-auto px-4">
       
        <div className="animate-pulse space-y-12">

          <div className="text-center space-y-4">
            <div className="h-12 bg-gray-700 rounded w-96 mx-auto"></div>
            <div className="h-6 bg-gray-700 rounded w-64 mx-auto"></div>
          </div>
          
          <div className="space-y-8">
            <div className="h-8 bg-gray-700 rounded w-48 mx-auto"></div>
            <div className="grid place-items-center grid-cols-2 md:grid-cols-6 gap-y-6 px-4">
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map((item) => (
                <div key={item} className="text-center animate-pulse">
                  <div className="w-16 h-16 bg-gray-700 rounded mx-auto mb-2"></div>
                  <div className="h-4 bg-gray-700 rounded w-16 mx-auto"></div>
                </div>
              ))}
            </div>
          </div>
       
          <div className="space-y-8">
            <div className="h-8 bg-gray-700 rounded w-48 mx-auto"></div>
            <div className="grid place-items-center grid-cols-2 md:grid-cols-6 gap-y-6 px-4">
              {[1, 2, 3, 4, 5, 6].map((item) => (
                <div key={item} className="text-center animate-pulse">
                  <div className="w-16 h-16 bg-gray-700 rounded mx-auto mb-2"></div>
                  <div className="h-4 bg-gray-700 rounded w-16 mx-auto"></div>
                </div>
              ))}
            </div>
          </div>
          
        
         
          <div className="space-y-8">
            <div className="h-8 bg-gray-700 rounded w-48 mx-auto"></div>
            <div className="grid place-items-center grid-cols-2 md:grid-cols-6 gap-y-6 px-4">
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map((item) => (
                <div key={item} className="text-center animate-pulse">
                  <div className="w-16 h-16 bg-gray-700 rounded mx-auto mb-2"></div>
                  <div className="h-4 bg-gray-700 rounded w-16 mx-auto"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
