@import "tailwindcss";
@import url("https://fonts.googleapis.com/css2?family=EB+Garamond:wght@400;500;600;700;800&family=Montserrat:wght@400;500;600;700;800&family=Arimo:wght@400;500;600;700&family=Migra:wght@400;500;600;700&family=Old+Standard+TT:wght@400;500;600&family=Open+Sans:wght@400;500;600;700&display=swap");

.font-ebgaramond {
  font-family: "EB Garamond", serif;
}

.font-migra {
  font-family: "Migra", serif;
}

.font-oldstandardtt {
  font-family: "Old Standard TT", serif;
}

.font-opensans {
  font-family: "Open Sans", sans-serif;
}

.font-montserrat {
  font-family: "Montserrat", sans-serif;
}

.font-arimo {
  font-family: "Arimo", sans-serif;
}

@font-face {
  font-family: "Montserrat Classic";
  src: url("/fonts/MontserratClassic.woff2") format("woff2");
  font-weight: normal;
  font-style: normal;
}

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Custom animations for enhanced navbar */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out forwards;
}

/* Enhanced header animations */
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes sparkle {
  0%, 100% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
  }
}

.animate-slideInFromTop {
  animation: slideInFromTop 0.7s ease-out forwards;
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

.animate-sparkle {
  animation: sparkle 2s infinite;
}

.animate-glow {
  animation: glow 3s infinite;
}

/* Smooth hover transitions */
.nav-link-hover {
  position: relative;
  overflow: hidden;
}

.nav-link-hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.nav-link-hover:hover::before {
  left: 100%;
}

/* Responsive enhancements */
@media (max-width: 640px) {
  .mobile-menu-slide {
    animation: slideInFromTop 0.4s ease-out forwards;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  /* Tablet specific styles */
  .tablet-nav-spacing {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* Enhanced focus states for accessibility */
.focus-ring:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Smooth transitions for all interactive elements */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Enhanced gradient backgrounds */
.bg-gradient-quality {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-gradient-solutions {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.bg-gradient-innovation {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.bg-gradient-responsibility {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}
