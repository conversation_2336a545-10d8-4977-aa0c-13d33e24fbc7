export default function RootLoading() {
  return (
    <div className="bg-black min-h-screen flex items-center justify-center">
      <div className="text-center">
        {/* Diamond Atelier Logo/Brand placeholder */}
        <div className="mb-8">
          <div className="w-24 h-24 bg-gradient-to-r from-gray-600 to-gray-400 rounded-full mx-auto mb-4 animate-pulse"></div>
          <div className="h-8 bg-gray-700 rounded w-64 mx-auto mb-2 animate-pulse"></div>
          <div className="h-4 bg-gray-700 rounded w-48 mx-auto animate-pulse"></div>
        </div>
        
        {/* Loading spinner */}
        <div className="relative">
          <div className="w-16 h-16 border-4 border-gray-700 border-t-white rounded-full animate-spin mx-auto"></div>
        </div>
        
        {/* Loading text */}
        <p className="text-white mt-6 font-montserrat">Loading Diamond Atelier...</p>
      </div>
    </div>
  );
}
