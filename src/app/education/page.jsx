import React from 'react'
import img1 from '../../../public/images/education/img1.png'
import img2 from '../../../public/images/education/img2.png'
import img3 from '../../../public/images/education/img3.png'
import img4 from '../../../public/images/education/img4.png'
import Image from 'next/image';
import dynamic from 'next/dynamic';

// Dynamic imports for client components
const AnimatedGrid = dynamic(() => import("@/components/common/AnimatedGrid"), {
  loading: () => <div className="animate-pulse bg-gray-200 h-64 rounded grid grid-cols-4 gap-4"></div>
});

const AOSWrapper = dynamic(() => import("@/components/common/AOSWrapper"));

// Metadata for SEO
export const metadata = {
  title: "Diamond Education - Learn About Lab Grown Diamonds | Diamond Atelier",
  description: "Learn about lab grown diamonds at Diamond Atelier. Discover the evolution of diamond creation and how artists shape stones into beautiful gems.",
  keywords: "diamond education, lab grown diamonds learning, diamond creation, diamond atelier education, diamond evolution",
  openGraph: {
    title: "Diamond Education - Lab Grown Diamond Learning",
    description: "Learn about the evolution of diamond creation and lab grown diamonds",
    type: "website",
  },
};

const page = () => {

  const evolution = [
    { id: 1, img: img1 },
    { id: 2, img: img2 },
    { id: 3, img: img3 },
    { id: 4, img: img4 },
  ]

  return (
    <AOSWrapper>
      <section className="relative pt-24 bg-black min-h-screen grid content-center cursor-pointer">
        <h2 data-aos="fade-down" className='text-[#6b6d6d] text-center text-2xl font-montserrat font-medium'>At Diamond Atelier, </h2>
        <p data-aos="fade-down" className='mb-10 text-[#6b6d6d] text-center text-6xl font-medium italic' style={{ fontFamily: 'Edwardian Script ITC' }}> artist shapes stones</p>
        <AnimatedGrid className='grid xl:grid-cols-4 md:grid-cols-4 grid-cols-2 place-items-center'>
          {evolution.map((item) => (
            <div className="text-center" key={item.id}>
              <Image
                src={item.img}
                alt="Evolution-img"
              />
            </div>
          ))}
        </AnimatedGrid>
      </section>
    </AOSWrapper>
  )
}

export default page