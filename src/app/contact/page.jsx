import React from 'react'
import background from '../../../public/images/banner/contact.png'
import Image from 'next/image'
import { FaPhone } from 'react-icons/fa'
import { FaLocationDot } from "react-icons/fa6";
import { SlGlobe } from "react-icons/sl";
import { IoMdMail } from "react-icons/io";
import dynamic from 'next/dynamic';

// Dynamic imports for client components
const ContactForm = dynamic(() => import("@/components/common/ContactForm"), {
  loading: () => <div className="animate-pulse bg-gray-200 h-64 rounded-2xl"></div>
});

const AOSWrapper = dynamic(() => import("@/components/common/AOSWrapper"));

// Metadata for SEO
export const metadata = {
  title: "Contact Diamond Atelier - Get in Touch | Lab Grown Diamond Experts",
  description: "Contact Diamond Atelier for premium lab grown diamonds. Located at 38 West 48th Street, New York. Email: <EMAIL>. Expert consultation available.",
  keywords: "contact diamond atelier, lab grown diamonds consultation, diamond experts New York, diamond atelier contact",
  openGraph: {
    title: "Contact Diamond Atelier - Lab Grown Diamond Experts",
    description: "Get in touch with Diamond Atelier for premium lab grown diamonds consultation",
    type: "website",
  },
};

function page() {
  return (
    <AOSWrapper>
      <div className='relative h-screen '>
        <div className="absolute inset-0">
          <Image
            src={background}
            alt="background Image"
            layout="fill"
            objectFit="fill"
            priority
          />
          <div className='relative xl:pt-48 md:pt-48 pt-16 px-6 grid grid-cols-1 md:grid-cols-2 xl:grid-cols-2 xl:gap-8 font-montserrat place-items-center cursor-pointer'>
          <div className='xl:space-y-6 space-y-2 mb-4'>
            <h2 data-aos="fade-right" className='text-white md:text-2xl xl:text-5xl text-xl font-semibold hover:text-[#777678]'>GET IN TOUCH WITH US</h2>
            <p data-aos="fade-right" className='text-white xl:text-xl md:text-sm text-sm hover:text-[#777678]'>We're here to help you with any questions or concerns</p>
            <div className='grid xl:grid-cols-2 xl:gap-6 gap-6' data-aos="fade-right">
              <div className='flex items-start gap-3' >
                <FaPhone className='text-black bg-white p-1 rounded-full size-8 md:mt-1 xl:mt-2 mt-0' />
                <div>
                  <h4 className='text-white font-medium xl:text-base md:text-sm text-xs hover:text-[#777678]'>Phone</h4>
                  <p className='text-white xl:text-base md:text-sm text-xs hover:text-[#777678]'>+************</p>
                </div>
              </div>
              <div className='flex items-start gap-3'>
                <FaPhone className='text-black bg-white p-1 rounded-full size-8 md:mt-1 xl:mt-2 mt-0' />
                <div>
                  <h4 className='text-white font-medium xl:text-base md:text-sm text-xs hover:text-[#777678]'>Phone</h4>
                  <p className='text-white xl:text-base md:text-sm text-xs hover:text-[#777678]'>+************</p>
                </div>
              </div>
              <div className='flex items-start gap-3'>
                <IoMdMail className='text-black bg-white p-1 rounded-full size-8 md:mt-1 xl:mt-2 mt-0' />
                <div>
                  <h4 className='text-white font-medium xl:text-base md:text-sm text-xs hover:text-[#777678]'>E-Mail</h4>
                  <p className='text-white xl:text-base md:text-sm text-xs hover:text-[#777678]'><EMAIL></p>
                </div>
              </div>
              <div className='flex items-start gap-3'>
                <SlGlobe className='text-black bg-white p-1 rounded-full size-8 md:mt-1 xl:mt-2 mt-0' />
                <div>
                  <h4 className='text-white font-medium xl:text-base md:text-sm text-xs hover:text-[#777678]'>Website</h4>
                  <p className='text-white xl:text-base md:text-sm text-xs hover:text-[#777678]'>www.diamondatelier.com</p>
                </div>
              </div>
              <div className='flex items-start gap-3 md:col-span-2'>
                <FaLocationDot className='text-black bg-white p-1 rounded-full size-8 md:mt-1 xl:mt-2 mt-0' />
                <div>
                  <h4 className='text-white font-medium xl:text-base md:text-sm text-xs hover:text-[#777678]'>Address</h4>
                  <p className='text-white xl:text-base md:text-sm text-xs hover:text-[#777678]'>38 West 48th Street, 5th Floor, New York, NY 10036</p>
                </div>
              </div>
            </div>
          </div>
            <ContactForm />
          </div>
        </div>
      </div>
    </AOSWrapper>
  )
}

export default page
